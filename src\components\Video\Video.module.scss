.wrapper {
  --size-data: calc(320px + 16vw);
  position: relative;
  display: flex;

  flex-direction: column;
  align-items: center;
  justify-content: center;
}
//user
// .user {
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   width: 100%;
// }
// .user-info {
//   display: flex;
//   padding: 8px;
//   align-items: center;
//   cursor: pointer;
// }
// .avatar {
//   width: 56px;
//   height: 56px;
//   border-radius: 999px;
//   object-fit: cover;
// }

// .item-info {
//   flex: 1;
//   margin-left: 12px;
// }
// .nickname {
//   font-size: 1.6rem;
//   font-weight: 700;
//   color: var(--text-color);
// }
// .check {
//   margin-left: 4px;
//   font-size: 1.4rem;
//   color: #20d5ec;
// }
// .name {
//   font-size: 1.3rem;
//   font-weight: 400;
//   color: #888;
// }
//VIDEO
.video {
  position: relative;
  display: flex;
  margin-top: 10px;
  align-items: flex-end;
}
.video-container {
  position: relative;
  border-radius: 8px;
  margin-right: 20px;
  overflow: hidden;
  height: var(--size-data);

  &:hover {
    .volume-slider,
    .volume-btn,
    .more-btn {
      opacity: 1;
    }
  }
}
.video-content {
  position: relative;
  cursor: pointer;
  height: 100%;
  width: 100%;
  border-radius: 8px;

  img {
    height: 100%;
    object-fit: cover;
    width: 100%;
    border-radius: 8px;
  }
  video {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    object-fit: cover;
    width: 100%;
    border-radius: 8px;
  }
}

.center-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  left: 50%;
  opacity: 1;
  // pointer-events: none;
  padding: 12px;
  background-color: #222;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: fadeOut 0.6s ease-in-out forwards;
}

.volume-container {
  color: var(--white);
  cursor: pointer;

  &.active {
    opacity: 1;

    .volume-slider {
      opacity: 0;
    }
  }
}
.volume-slider {
  display: flex;
  width: 64px;
  height: 25px;
  background-color: rgba(22, 24, 35, 0.34);
  border-radius: 32px;
  position: absolute;
  top: 12px;
  left: 38px;
  opacity: 0;

  input {
    position: absolute;
    top: 12px;
    right: 7px;
    -webkit-appearance: none;
    background-color: var(--white);
    height: 2px;
    width: 50px;

    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 12px;
      height: 12px;
      background-color: var(--white);
      border-radius: 999px;
      cursor: pointer;
    }
  }
}
.volume-btn {
  position: absolute;
  top: 12px;
  left: 10px;
  opacity: 0;
}
.more-btn {
  position: absolute;
  top: 12px;
  right: 10px;
  color: var(--white);
  cursor: pointer;
  opacity: 0;
}
.video-interaction {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    border-radius: 50%;
    color: var(--text-color);
    background-color: rgba(22, 24, 35, 0.12);
    margin-top: 10px;

    cursor: pointer;
  }

  strong {
    font-size: 1.6rem;
  }
}
.profile {
  position: relative;
  height: 60px;
}
.avatar {
  width: 4.4rem;
  height: 4.4rem;
  border-radius: 999px;
  object-fit: cover;
}
.follow-btn {
  position: absolute;
  top: 34px;
  left: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  padding: 4px;
  font-size: 1.4rem;
  border-radius: 50%;
  background-color: var(--primary);
  color: var(--white);
  cursor: pointer;

  .icon {
    width: 14px;
    height: 14px;
  }

  &.followed {
    background-color: rgba(255, 255, 255, 1);
    border: 1px solid rgba(22, 24, 35, 0.12);

    .icon {
      color: var(--primary);
    }
  }
}
@keyframes fadeOut {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.3);
  }
}
